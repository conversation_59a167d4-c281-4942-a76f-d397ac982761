import React from "react";

const FilterSortBar = ({
  showFilters,
  setShowFilters,
  showSorting,
  setShowSorting,
  isFiltersApplied,
  appliedFilters,
  appliedSorting,
}) => {
  // Count active filters
  const activeFilterCount = Object.keys(appliedFilters).filter(key => 
    Array.isArray(appliedFilters[key]) ? appliedFilters[key].length > 0 : appliedFilters[key]
  ).length;

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex gap-4 items-center">
        {/* Sorting Button */}
        <button
          onClick={() => setShowSorting(!showSorting)}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
            showSorting || appliedSorting
              ? 'bg-blue-50 border-blue-200 text-blue-700'
              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <span className="text-lg">+</span>
          <span className="text-sm font-medium">Sorting</span>
          {appliedSorting && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              {appliedSorting}
            </span>
          )}
        </button>

        {/* Filter Button */}
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
            showFilters || isFiltersApplied
              ? 'bg-blue-50 border-blue-200 text-blue-700'
              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <span className="text-lg">+</span>
          <span className="text-sm font-medium">Filter</span>
          {isFiltersApplied && activeFilterCount > 0 && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              {activeFilterCount}
            </span>
          )}
        </button>
      </div>
    </div>
  );
};

export default FilterSortBar;
