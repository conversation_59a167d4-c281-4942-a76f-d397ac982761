import React, { useEffect, useState } from "react";
import { unstable_batchedUpdates } from "react-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import { useLocation, useNavigate } from "react-router-dom";
import CandidateTable from "../../components/core/recruiter/candidates/CandidateTable";
import AppButton from "../../components/common/Button/AppButton";
import { getSubmission, getMaxExperience } from "../../services/operations/candidateAPI";
import { JobStatusOptions } from "../../utils/JobStatusOptions";
import EnhancedThreeDot from "../../components/common/Button/EnhancedThreeDot";
import LocationDropdown from "../../components/core/recruiter/candidates/LocationDropdown";
import StatusDropdown from "../../components/core/recruiter/candidates/StatusDropdown";
import JobTypeDropdown from "../../components/core/recruiter/candidates/JobTypeDropdown";
import ExperienceLevelDropdown from "../../components/core/recruiter/candidates/ExperienceLevelDropdown";
import FilterBar from "../../components/core/recruiter/candidates/FilterBar";
import UniversalSearchDropdown from "../../components/core/recruiter/shared/UniversalSearchDropdown";
import SubmissionDateSortPill from "../../components/core/recruiter/candidates/SubmissionDateSortPill";

const Candidates = () => {
  const { search, pathname } = useLocation();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(search);
  const currentTabs = searchParams.get("tabs");

  // Helper function to update URL with current state (tab-specific)
  const updateURL = (newParams = {}, tabIndex = active) => {
    const urlParams = new URLSearchParams(search);

    // Always preserve the tabs parameter
    if (currentTabs !== null) {
      urlParams.set("tabs", currentTabs);
    }

    // Clear existing tab-specific parameters for current tab
    const tabPrefix = `tab${tabIndex}_`;
    Array.from(urlParams.keys()).forEach(key => {
      if (key.startsWith(tabPrefix)) {
        urlParams.delete(key);
      }
    });

    // Update or add tab-specific parameters
    Object.entries(newParams).forEach(([key, value]) => {
      if (value && value !== "" && !(Array.isArray(value) && value.length === 0)) {
        const tabSpecificKey = `${tabPrefix}${key}`;
        if (Array.isArray(value)) {
          urlParams.set(tabSpecificKey, value.join(','));
        } else {
          urlParams.set(tabSpecificKey, value);
        }
      }
    });

    navigate(`${pathname}?${urlParams.toString()}`, { replace: true });
  };

  // Helper function to parse URL parameters and restore state (tab-specific)
  const parseURLParams = (tabIndex = active) => {
    const params = new URLSearchParams(search);
    const parsedState = {};
    const tabPrefix = `tab${tabIndex}_`;

    // Parse tab-specific location
    const location = params.get(`${tabPrefix}location`);
    if (location) {
      parsedState.locationSelected = location.split(',');
    }

    // Parse tab-specific status
    const status = params.get(`${tabPrefix}status`);
    if (status) {
      parsedState.statusSelected = status.split(',');
    }

    // Parse tab-specific jobType
    const jobType = params.get(`${tabPrefix}jobType`);
    if (jobType) {
      parsedState.jobTypeSelected = jobType.split(',');
    }

    // Parse tab-specific experienceLevel
    const experienceLevel = params.get(`${tabPrefix}experienceLevel`);
    if (experienceLevel) {
      const [min, max] = experienceLevel.split('-').map(Number);
      if (!isNaN(min) && !isNaN(max)) {
        parsedState.experienceLevelSelected = [min, max];
      }
    }

    // Parse global sorting (like Jobs section)
    const submissionDate = params.get('submissionDate');
    if (submissionDate) {
      parsedState.sortValue = submissionDate;
    }

    // Parse global search parameters (not tab-specific anymore)
    const searchTerm = params.get('searchTerm') || params.get(`${tabPrefix}searchTerm`);
    const searchField = params.get('searchField') || params.get(`${tabPrefix}searchField`);
    if (searchTerm && searchField) {
      parsedState.searchTerm = searchTerm;
      parsedState.searchField = searchField;
    }

    return parsedState;
  };

  // Helper function to save current tab state (filters only, search is global)
  const saveCurrentTabState = (tabIndex = active) => {
    const currentState = {
      locationSelected,
      statusSelected,
      jobTypeSelected,
      experienceLevelSelected,
      // Note: Sorting is now global and not saved per tab (like Jobs section)
      appliedFilters,
      isFiltersApplied
    };



    setTabStates(prev => ({
      ...prev,
      [tabIndex]: currentState
    }));
  };









  // Enhanced inheritance function that preserves filters across tabs even when passing through unsupported tabs
  const inheritCompatibleFiltersWithGlobalState = (fromTabState, fromTabIndex, toTabIndex, updatedTabStates) => {
    const toTabOptions = tabFilterOptions[toTabIndex];

    // Update global inheritance state with current tab's filters for future inheritance
    updateGlobalInheritanceState(fromTabState, fromTabIndex);

    // If target tab has no filter options, return state with only sorting (all tabs support sorting)
    if (!toTabOptions || toTabOptions.length === 0) {
      const inheritanceSource = globalInheritanceState;

      return {
        locationSelected: [],
        statusSelected: [],
        jobTypeSelected: [],
        experienceLevelSelected: [1, 12],
        sortValue: inheritanceSource.sortValue || "recent", // Inherit sorting even for tabs with no filter options
        appliedFilters: {},
        isFiltersApplied: inheritanceSource.sortValue && inheritanceSource.sortValue !== "recent" // Mark as applied if sorting is inherited
      };
    }

    // Start with target tab's current state
    const targetTabState = updatedTabStates[toTabIndex] || {
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false
    };
    const inheritedState = { ...targetTabState };

    // Use global inheritance state as the source of truth for inheritance
    const inheritanceSource = globalInheritanceState;
    let hasInheritedFilters = false;
    const inheritedFilters = { ...targetTabState.appliedFilters };

    // Inherit location if target supports it and global state has location filters
    if (toTabOptions.includes('location') && inheritanceSource.locationSelected?.length > 0) {
      inheritedState.locationSelected = [...inheritanceSource.locationSelected];
      inheritedFilters.location = [...inheritanceSource.locationSelected];
      hasInheritedFilters = true;
    }

    // Inherit status if target supports it and global state has status filters
    if (toTabOptions.includes('status') && inheritanceSource.statusSelected?.length > 0) {
      inheritedState.statusSelected = [...inheritanceSource.statusSelected];
      inheritedFilters.status = [...inheritanceSource.statusSelected];
      hasInheritedFilters = true;
    }

    // Inherit jobType if target supports it and global state has jobType filters
    if (toTabOptions.includes('jobType') && inheritanceSource.jobTypeSelected?.length > 0) {
      inheritedState.jobTypeSelected = [...inheritanceSource.jobTypeSelected];
      inheritedFilters.jobType = [...inheritanceSource.jobTypeSelected];
      hasInheritedFilters = true;
    }

    // Inherit experienceLevel if target supports it and global state has non-default experience
    if (toTabOptions.includes('experienceLevel') && inheritanceSource.experienceLevelSelected &&
        (inheritanceSource.experienceLevelSelected[0] !== 1 || inheritanceSource.experienceLevelSelected[1] !== 12)) {
      inheritedState.experienceLevelSelected = [...inheritanceSource.experienceLevelSelected];
      inheritedFilters.experienceLevel = [`${inheritanceSource.experienceLevelSelected[0]}-${inheritanceSource.experienceLevelSelected[1]}`];
      hasInheritedFilters = true;
    }

    // Note: Sorting is now global and independent of inheritance logic (like Jobs section)

    // Update applied filters and mark as applied if any inheritance occurred OR target had existing filters
    const targetHadFilters = targetTabState.isFiltersApplied;
    if (hasInheritedFilters || targetHadFilters) {
      inheritedState.appliedFilters = inheritedFilters;
      inheritedState.isFiltersApplied = true;
    }

    // Update global inheritance state with current tab's filters
    updateGlobalInheritanceState(fromTabState, fromTabIndex);

    return inheritedState;
  };

  // Helper function to update global inheritance state
  const updateGlobalInheritanceState = (tabState, tabIndex) => {
    const tabOptions = tabFilterOptions[tabIndex];

    // Note: Sorting is now global and independent of inheritance logic (like Jobs section)
    setGlobalInheritanceState(prev => {
      const updated = { ...prev };

      // If tab has no filter options, return early (sorting is handled globally)
      if (!tabOptions || tabOptions.length === 0) {
        updated.lastUpdatedTab = tabIndex;
        return updated;
      }

      // Continue with filter updates for tabs that support filters
      return updated;
    });

    // If tab has no filter options, we're done (sorting already handled above)
    if (!tabOptions || tabOptions.length === 0) return;

    // Update filter-specific state for tabs that support filters
    setGlobalInheritanceState(prev => {
      const updated = { ...prev };

      // Update each filter type if the current tab supports it and has values
      if (tabOptions.includes('location') && tabState.locationSelected?.length > 0) {
        updated.locationSelected = [...tabState.locationSelected];
        updated.appliedFilters = { ...updated.appliedFilters, location: [...tabState.locationSelected] };
      }

      if (tabOptions.includes('status') && tabState.statusSelected?.length > 0) {
        updated.statusSelected = [...tabState.statusSelected];
        updated.appliedFilters = { ...updated.appliedFilters, status: [...tabState.statusSelected] };
      }

      if (tabOptions.includes('jobType') && tabState.jobTypeSelected?.length > 0) {
        updated.jobTypeSelected = [...tabState.jobTypeSelected];
        updated.appliedFilters = { ...updated.appliedFilters, jobType: [...tabState.jobTypeSelected] };
      }

      if (tabOptions.includes('experienceLevel') && tabState.experienceLevelSelected &&
          (tabState.experienceLevelSelected[0] !== 1 || tabState.experienceLevelSelected[1] !== 12)) {
        updated.experienceLevelSelected = [...tabState.experienceLevelSelected];
        updated.appliedFilters = { ...updated.appliedFilters, experienceLevel: [`${tabState.experienceLevelSelected[0]}-${tabState.experienceLevelSelected[1]}`] };
      }

      // Always update sorting (already handled above, but ensure consistency)
      if (tabState.sortValue && tabState.sortValue !== "recent") {
        updated.sortValue = tabState.sortValue;
      }

      // Update isFiltersApplied based on whether any filters are actually applied
      const hasFilters = Object.keys(updated.appliedFilters).some(key =>
        Array.isArray(updated.appliedFilters[key]) ? updated.appliedFilters[key].length > 0 : updated.appliedFilters[key]
      );
      updated.isFiltersApplied = hasFilters || (updated.sortValue && updated.sortValue !== "recent");
      updated.lastUpdatedTab = tabIndex;

      return updated;
    });
  };


  const tabs = [
    { name: <span>Active Submissions</span>, css: "" },
    { name: <span>Hired Candidates</span>, css: "" },
    { name: <span>Rejected Candidates</span>, css: "" },
    { name: <span>All Submissions</span>, css: "" },
  ];
  const [active, setActive] = useState(0);
  const [showFilter, setShowFilter] = useState(false);
  const [locationSelected, setLocationSelected] = useState([]);
  const [statusSelected, setStatusSelected] = useState([]);
  const [showSort, setShowSort] = useState(false);
  const [sortValue, setSortValue] = useState("recent");
  const [appliedSorting, setAppliedSorting] = useState(""); // Global applied sorting like Jobs section
  const [jobTypeSelected, setJobTypeSelected] = useState([]);
  const [experienceLevelSelected, setExperienceLevelSelected] = useState([1, 12]);
  const [maxExperience, setMaxExperience] = useState(12);
  // Global search dropdown state (not tab-specific)
  const [showSearch, setShowSearch] = useState(false);

  // Tab-specific filter states
  const [appliedFilters, setAppliedFilters] = useState({});
  const [isFiltersApplied, setIsFiltersApplied] = useState(false);
  const [shouldShowFilterBar, setShouldShowFilterBar] = useState(false);

  // Global search state (works across all tabs)
  const [globalSearchState, setGlobalSearchState] = useState({
    searchTerm: "",
    searchField: "all",
    isSearchApplied: false
  });

  // Define which filter options are available for each tab
  const tabFilterOptions = {
    0: ['location', 'status'], // Active Submissions
    1: ['location', 'jobType', 'experienceLevel'], // Hired Candidates - removed status
    2: [], // Rejected Candidates - no filtering (to avoid conflict with tab-based rejected filtering)
    3: ['location', 'status', 'jobType', 'experienceLevel'] // All Submissions
  };



  // Global filter inheritance state - preserves filters across all tabs for inheritance
  const [globalInheritanceState, setGlobalInheritanceState] = useState({
    locationSelected: [],
    statusSelected: [],
    jobTypeSelected: [],
    experienceLevelSelected: [1, 12],
    sortValue: "recent",
    appliedFilters: {},
    isFiltersApplied: false,
    lastUpdatedTab: null // Track which tab last updated each filter type
  });

  // Tab-specific state storage - each tab remembers its own filters/sort (search is global)
  const [tabStates, setTabStates] = useState({
    0: { // Active Submissions
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false
    },
    1: { // Hired Candidates
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false
    },
    2: { // Rejected Candidates
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false
    },
    3: { // All Submissions
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false
    }
  });

  const filterOptions = {
    0: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location",
      },
      {
        label: "Status",
        component: (
          <StatusDropdown
            selected={statusSelected}
            setSelected={setStatusSelected}
          />
        ),
        selected: statusSelected,
        placeholder: "Select Status",
        key: "status",
      },
    ],
    1: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location1",
      },
      {
        label: "Job Type",
        component: (
          <JobTypeDropdown
            selected={jobTypeSelected}
            setSelected={setJobTypeSelected}
          />
        ),
        selected: jobTypeSelected,
        placeholder: "Select Job Type",
        key: "jobType1",
      },
      {
        label: "Experience Level",
        component: (
          <ExperienceLevelDropdown
            selected={experienceLevelSelected}
            setSelected={setExperienceLevelSelected}
            min={1}
            max={maxExperience}
          />
        ),
        selected: experienceLevelSelected,
        placeholder: "Select Experience Level",
        key: "experienceLevel1",
      },
    ],
    3: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location3",
      },
      {
        label: "Status",
        component: (
          <StatusDropdown
            selected={statusSelected}
            setSelected={setStatusSelected}
          />
        ),
        selected: statusSelected,
        placeholder: "Select Status",
        key: "status3",
      },
      {
        label: "Job Type",
        component: (
          <JobTypeDropdown
            selected={jobTypeSelected}
            setSelected={setJobTypeSelected}
          />
        ),
        selected: jobTypeSelected,
        placeholder: "Select Job Type",
        key: "jobType3",
      },
      {
        label: "Experience Level",
        component: (
          <ExperienceLevelDropdown
            selected={experienceLevelSelected}
            setSelected={setExperienceLevelSelected}
            min={1}
            max={maxExperience}
          />
        ),
        selected: experienceLevelSelected,
        placeholder: "Select Experience Level",
        key: "experienceLevel3",
      },
    ],
  };



  // Add a flag to track if we're in the middle of a manual tab switch
  const [isManualTabSwitch, setIsManualTabSwitch] = useState(false);

  // Effect to restore state from URL parameters on page load/refresh (tab-specific)
  useEffect(() => {
    // Skip URL restoration if we're in the middle of a manual tab switch
    if (isManualTabSwitch) {

      setIsManualTabSwitch(false);
      return;
    }

    const urlState = parseURLParams(active);

    // Only restore state if there are URL parameters for current tab
    if (Object.keys(urlState).length > 0) {


      // Restore filter selections from URL
      if (urlState.locationSelected) {
        setLocationSelected(urlState.locationSelected);
      }
      if (urlState.statusSelected) {
        setStatusSelected(urlState.statusSelected);
      }
      if (urlState.jobTypeSelected) {
        setJobTypeSelected(urlState.jobTypeSelected);
      }
      if (urlState.experienceLevelSelected) {
        setExperienceLevelSelected(urlState.experienceLevelSelected);
      }
      if (urlState.sortValue) {
        setSortValue(urlState.sortValue);
        // Set applied sorting if it's not the default
        if (urlState.sortValue !== "recent") {
          setAppliedSorting(urlState.sortValue);
        }
      }

      // Also check for submissionDate parameter in URL (for applied sorting)
      const urlParams = new URLSearchParams(window.location.search);
      const submissionDate = urlParams.get('submissionDate');
      if (submissionDate && submissionDate !== "recent") {
        setSortValue(submissionDate);
        setAppliedSorting(submissionDate);
      }

      // Build filters object for API call
      const filters = {};

      if (urlState.locationSelected) filters.location = urlState.locationSelected;
      if (urlState.statusSelected) filters.status = urlState.statusSelected;
      if (urlState.jobTypeSelected) filters.jobType = urlState.jobTypeSelected;
      if (urlState.experienceLevelSelected) {
        const [min, max] = urlState.experienceLevelSelected;
        if (min !== 1 || max !== 12) {
          filters.experienceLevel = [`${min}-${max}`];
        }
      }
      // Sorting is handled separately via appliedSorting (like Jobs section)

      // Handle global search state separately (don't add to filters)
      if (urlState.searchTerm && urlState.searchField) {
        setGlobalSearchState({
          searchTerm: urlState.searchTerm,
          searchField: urlState.searchField,
          isSearchApplied: true
        });
      } else {
        // Clear global search state if no search parameters in URL
        setGlobalSearchState({
          searchTerm: "",
          searchField: "all",
          isSearchApplied: false
        });
      }

      // Apply filters if any exist (excluding search)
      if (Object.keys(filters).length > 0) {
        setAppliedFilters(filters);

        // Set isFiltersApplied to true when filters exist (search is handled separately)
        setIsFiltersApplied(true);

        // Check if there are actual non-sort filters applied (search is handled separately)
        const hasNonSortFilters = filters.location || filters.status ||
                                 filters.jobType || filters.experienceLevel;
        const hasActualFilters = hasNonSortFilters || (filters.sortBy && filters.sortBy !== "recent");

        // Set filter bar visibility based on non-sort filters only (sorting doesn't require filter bar)
        setShouldShowFilterBar(hasNonSortFilters);

        // Show filter bar if actual filters are restored from URL
        if (hasActualFilters) {
          const hasFilterOptions = tabFilterOptions[active] && tabFilterOptions[active].length > 0;
          const hasNonSortFilters = filters.location || filters.status || filters.jobType || filters.experienceLevel;

          // Show filter bar only if there are filter options AND actual non-sort filters applied
          if (hasFilterOptions && hasNonSortFilters) {
            setShowFilter(true);
          }
          // Show sort section if global sorting is applied
          if (submissionDate && submissionDate !== "recent") {
            setShowSort(true);
          }
        }
      }

      // Save restored state to tab storage
      saveCurrentTabState(active);
    }
  }, [search, active]); // Re-run when URL search params change or active tab changes

  // Debug: Track appliedSorting changes
  useEffect(() => {
    console.log('🔄 COMPONENT RE-RENDER - appliedSorting changed:', {
      appliedSorting,
      currentTab: active,
      timestamp: new Date().toISOString()
    });
  }, [appliedSorting, active]);

  // Note: Sorting is handled in handleApplyFilter when "Apply" is clicked

  useEffect(() => {
    if (currentTabs) {
      setActive(Number(currentTabs));
      // Don't automatically hide filter/sort bars here - let the tab switching logic handle it
      // Keep search dropdown open when switching tabs for better UX
    } else {
      setActive(0);
      setShowFilter(false);
      setShowSort(false);
      setAppliedFilters({});
      setIsFiltersApplied(false);
      setShouldShowFilterBar(false);
    }
  }, [currentTabs]);

  // Initial URL setup - ensure tabs parameter exists on first load
  useEffect(() => {
    // Only set initial tabs parameter if there's no tabs parameter in URL
    const urlParams = new URLSearchParams(search);
    if (!urlParams.has('tabs')) {
      navigate(`${pathname}?tabs=0`, { replace: true });
    }
  }, );

  useEffect(() => {
    async function fetchMaxExp() {
      try {
        const response = await getMaxExperience();
        const maxExp = Math.max(response.maxExperience || 12, 12);
        setMaxExperience(maxExp);
        // If current selected is out of new max, reset
        setExperienceLevelSelected(([min, max]) => [min, Math.min(max, maxExp)]);
      } catch (error) {
        console.error("Error fetching max experience:", error);
        setMaxExperience(12); // fallback to 12
      }
    }
    fetchMaxExp();
  }, []);

  // useEffect to handle filter bar visibility based on tab state changes (only for automatic showing)
  useEffect(() => {
    const currentTabState = tabStates[active];
    const hasFilterOptions = tabFilterOptions[active] && tabFilterOptions[active].length > 0;

    // Only automatically show filter/sort sections when there are applied filters
    // Don't automatically hide them to allow manual user interactions
    if (currentTabState?.shouldShowFilterBar) {
      const hasNonSortFilters = currentTabState.appliedFilters?.location ||
                               currentTabState.appliedFilters?.status ||
                               currentTabState.appliedFilters?.jobType ||
                               currentTabState.appliedFilters?.experienceLevel;

      // Show filter bar only if there are filter options AND actual non-sort filters applied
      if (hasFilterOptions && hasNonSortFilters) {
        setShowFilter(true);
      }
      // Show sort section if sorting is applied
      if (currentTabState.sortValue && currentTabState.sortValue !== "recent") {
        setShowSort(true);
      }
    }
    // Note: We don't automatically hide filter/sort sections here to allow manual user interactions
  }, [active, tabStates, tabFilterOptions]);

  // Handler for manual tab clicks from TabNav component
  function handleTabClick(tabIndex) {
    // Set flag to prevent URL restoration from interfering with inheritance
    setIsManualTabSwitch(true);

    // Create current tab state object directly from component state (more reliable than async tabStates)
    // Search is global, so no need to save it per tab
    const currentTabState = {
      locationSelected,
      statusSelected,
      jobTypeSelected,
      experienceLevelSelected,
      // Note: Sorting is now global and not saved per tab (like Jobs section)
      appliedFilters,
      isFiltersApplied
    };

    // Save current tab state synchronously to tabStates for immediate use
    const updatedTabStates = {
      ...tabStates,
      [active]: currentTabState
    };

    // Update tabStates immediately
    setTabStates(updatedTabStates);

    // Get inherited state for target tab using the enhanced global inheritance logic
    const inheritedState = inheritCompatibleFiltersWithGlobalState(currentTabState, active, tabIndex, updatedTabStates);





    // Update target tab state with inherited filters
    setTabStates(prev => ({
      ...prev,
      [tabIndex]: inheritedState
    }));

    // Update URL to show new tab and its state
    const urlParams = new URLSearchParams();
    urlParams.set("tabs", tabIndex);

    // Add inherited/existing state to URL if any filters are applied
    if (inheritedState && inheritedState.isFiltersApplied) {
      const tabPrefix = `tab${tabIndex}_`;

      if (inheritedState.locationSelected?.length > 0) {
        urlParams.set(`${tabPrefix}location`, inheritedState.locationSelected.join(','));
      }
      if (inheritedState.statusSelected?.length > 0) {
        urlParams.set(`${tabPrefix}status`, inheritedState.statusSelected.join(','));
      }
      if (inheritedState.jobTypeSelected?.length > 0) {
        urlParams.set(`${tabPrefix}jobType`, inheritedState.jobTypeSelected.join(','));
      }
      if (inheritedState.experienceLevelSelected &&
          (inheritedState.experienceLevelSelected[0] !== 1 || inheritedState.experienceLevelSelected[1] !== 12)) {
        urlParams.set(`${tabPrefix}experienceLevel`, `${inheritedState.experienceLevelSelected[0]}-${inheritedState.experienceLevelSelected[1]}`);
      }
      // Global sorting is handled separately (like Jobs section)
      if (inheritedState.appliedFilters?.searchTerm && inheritedState.appliedFilters?.searchField) {
        urlParams.set(`${tabPrefix}searchTerm`, inheritedState.appliedFilters.searchTerm);
        urlParams.set(`${tabPrefix}searchField`, inheritedState.appliedFilters.searchField);
      }
    }

    navigate(`${pathname}?${urlParams.toString()}`, { replace: true });

    setActive(tabIndex);
    // Keep search dropdown open when switching tabs for better UX

    // Initially hide filter and sort bars, but they will be shown if inherited filters exist
    setShowFilter(false);
    setShowSort(false);

    // Apply the inherited state to current component state (batched for performance)
    // Search state is global and doesn't need inheritance
    unstable_batchedUpdates(() => {
      setLocationSelected(inheritedState.locationSelected);
      setStatusSelected(inheritedState.statusSelected);
      setJobTypeSelected(inheritedState.jobTypeSelected);
      setExperienceLevelSelected(inheritedState.experienceLevelSelected);
      // Note: Sorting is global and not inherited per tab (like Jobs section)
      setAppliedFilters(inheritedState.appliedFilters);
      setIsFiltersApplied(inheritedState.isFiltersApplied);
      setShouldShowFilterBar(inheritedState.shouldShowFilterBar || false);
    });

    // Show filter bar if inherited filters exist
    if (inheritedState.shouldShowFilterBar) {
      const hasFilterOptions = tabFilterOptions[tabIndex] && tabFilterOptions[tabIndex].length > 0;
      const hasNonSortFilters = inheritedState.appliedFilters?.location ||
                               inheritedState.appliedFilters?.status ||
                               inheritedState.appliedFilters?.jobType ||
                               inheritedState.appliedFilters?.experienceLevel;

      // Show filter bar only if there are filter options AND actual non-sort filters applied
      if (hasFilterOptions && hasNonSortFilters) {
        setShowFilter(true);
      }
      // Show sort section if global sorting is applied (like Jobs section)
      if (appliedSorting && appliedSorting !== "") {
        setShowSort(true);
      }
    }

    // Save the inherited state to ensure it's persisted (after state updates)
    saveCurrentTabState(tabIndex);


  }

  async function getSubmissionDetail(
    submissionType = "activeSubmission",
    page,
    limit,
    filters = {}
  ) {
    // Handle sorting like Jobs section - use appliedSorting for API calls
    const filtersForAPI = { ...filters };

    // Add applied sorting to API call (backend expects sortBy parameter)
    if (appliedSorting) {
      filtersForAPI.sortBy = appliedSorting === "recent" ? "newest" : "oldest";
    } else {
      filtersForAPI.sortBy = "newest"; // Default sorting
    }

    // Debug logging
    console.log('📡 API CALL - getSubmissionDetail:', {
      submissionType,
      appliedSorting,
      sortBy: filtersForAPI.sortBy,
      page,
      limit,
      filtersForAPI,
      timestamp: new Date().toISOString()
    });

    const result = await getSubmission(submissionType, page, limit, filtersForAPI);
    return {
      resData: result.results,
      totalResults: result.total,
      totalPages: result.totalPages,
      page: result.page,
      limit: result.limit,
    };
  }

  // Prevent navigation/redirect on filter/sort click
  function handleFilterClick(e) {
    e?.stopPropagation?.();
    setShowFilter((prev) => !prev);
  }
  function handleSortClick(e) {
    e?.stopPropagation?.();
    setShowSort((prev) => !prev);
  }

  // Global sorting handler - only stores value, actual application happens in handleApplyFilter
  function handleSortingChange(newSortValue) {
    setSortValue(newSortValue);
    // Don't apply sorting here, wait for Apply button
  }
  function handleClearFilter() {
    // Clear all filter selections for current component state (batched for performance)
    unstable_batchedUpdates(() => {
      setLocationSelected([]);
      setStatusSelected([]);
      setJobTypeSelected([]);
      setExperienceLevelSelected([1, maxExperience]);
      setSortValue("recent");

      // Clear applied filters and sorting
      setAppliedFilters({});
      setIsFiltersApplied(false);
      setAppliedSorting(""); // Clear applied sorting
      setShouldShowFilterBar(false);

      // Clear global search state
      setGlobalSearchState({
        searchTerm: "",
        searchField: "all",
        isSearchApplied: false
      });
    });

    // Clear global inheritance state
    setGlobalInheritanceState({
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false,
      lastUpdatedTab: null
    });

    // Clear ALL tabs' state from storage (filters only, search is global)
    const defaultState = {
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false
    };

    setTabStates({
      0: { ...defaultState },
      1: { ...defaultState },
      2: { ...defaultState },
      3: { ...defaultState }
    });

    // Clear ALL tab-specific URL parameters (global clear)
    const urlParams = new URLSearchParams();

    // Keep only tabs parameter
    if (currentTabs !== null) {
      urlParams.set("tabs", currentTabs);
    }

    // Ensure sorting parameters are also cleared from URL
    urlParams.delete('submissionDate');

    navigate(`${pathname}?${urlParams.toString()}`, { replace: true });

    // Hide filter, sort, and search sections (batched for performance)
    unstable_batchedUpdates(() => {
      setShowFilter(false);
      setShowSort(false);
      setShowSearch(false); // Close global search dropdown
    });
  }
  // Replace handleApplyFilter to use applyQuery
  function handleApplyFilter() {
    console.log('🚀 APPLY BUTTON CLICKED - START', {
      currentTab: active,
      sortValue,
      appliedSorting,
      timestamp: new Date().toISOString()
    });

    // Build filter object based on current selections
    const filters = {};
    const urlParams = {};

    // Location filter
    if (locationSelected && locationSelected.length > 0) {
      filters.location = locationSelected;
      urlParams.location = locationSelected;
    }

    // Status filter (exclude for hired candidates tab)
    if (statusSelected && statusSelected.length > 0 && active !== 1) {
      filters.status = statusSelected;
      urlParams.status = statusSelected;
    }

    // Job Type filter
    if (jobTypeSelected && jobTypeSelected.length > 0) {
      filters.jobType = jobTypeSelected;
      urlParams.jobType = jobTypeSelected;
    }

    // Experience level filter
    if (experienceLevelSelected && experienceLevelSelected.length === 2) {
      const [min, max] = experienceLevelSelected;
      if (min !== 1 || max !== 12) { // Only apply if not default range
        filters.experienceLevel = [`${min}-${max}`]; // Send as array for consistency
        urlParams.experienceLevel = `${min}-${max}`;
      }
    }

    // Note: Global sorting will be handled at the end of this function

    // Preserve existing search parameters if they exist
    if (appliedFilters.searchTerm && appliedFilters.searchField) {
      filters.searchTerm = appliedFilters.searchTerm;
      filters.searchField = appliedFilters.searchField;
      urlParams.searchTerm = appliedFilters.searchTerm;
      urlParams.searchField = appliedFilters.searchField;
    }

    // Store applied filters and mark as applied
    setAppliedFilters(filters);
    setIsFiltersApplied(true);

    // Set filter bar visibility based on non-sort filters only
    const hasNonSortFilters = filters.location || filters.status || filters.jobType || filters.experienceLevel;
    setShouldShowFilterBar(hasNonSortFilters);

    // Update URL with current filter state (tab-specific) including sorting for proper state restoration
    updateURL(urlParams, active);

    // Save current state to tab storage immediately (synchronous)
    const currentState = {
      locationSelected,
      statusSelected,
      jobTypeSelected,
      experienceLevelSelected,
      // Note: Sorting is now global and not saved per tab (like Jobs section)
      appliedFilters: filters, // Use the new filters
      isFiltersApplied: true
    };

    setTabStates(prev => ({
      ...prev,
      [active]: currentState
    }));

    // Update global inheritance state with the newly applied filters
    updateGlobalInheritanceState(currentState, active);

    // Hide filter and sort sections after applying, but keep filter bar visible if filters are applied
    const hasFilterOptions = tabFilterOptions[active] && tabFilterOptions[active].length > 0;
    const shouldShowFilterAfterApply = hasFilterOptions && hasNonSortFilters;
    setShowFilter(shouldShowFilterAfterApply);
    setShowSort(false);

    // Handle global sorting (applied when "Apply" is clicked)
    const currentSorting = sortValue && sortValue !== "recent" ? sortValue : "";

    // Update applied sorting state
    setAppliedSorting(currentSorting);
    
    // Add sorting to URL parameters
    if (currentSorting) {
      urlParams.submissionDate = currentSorting;
    }

    // The table will re-render naturally due to appliedSorting state change

    console.log('🏁 APPLY BUTTON CLICKED - END', {
      currentTab: active,
      finalSortValue: sortValue,
      expectedAppliedSorting: currentSorting,
      timestamp: new Date().toISOString()
    });
  }

  // Global search dropdown toggle - works across all tabs
  function handleSearchClick() {
    setShowSearch(prev => !prev);
  }

  // Global search handler - works across all tabs
  function handleSearch(searchData) {
    const hasSearchTerm = searchData.searchTerm.trim() !== "";

    // Update global search state
    unstable_batchedUpdates(() => {
      setGlobalSearchState({
        searchTerm: hasSearchTerm ? searchData.searchTerm : "",
        searchField: searchData.searchField,
        isSearchApplied: hasSearchTerm
      });
    });

    // Update URL with search parameters (global, not tab-specific)
    // Get current URL parameters
    const currentUrlParams = new URLSearchParams(search);

    // Preserve existing filter parameters for current tab
    const urlParams = {};
    if (appliedFilters.location) urlParams.location = appliedFilters.location;
    if (appliedFilters.status) urlParams.status = appliedFilters.status;
    if (appliedFilters.jobType) urlParams.jobType = appliedFilters.jobType;
    if (appliedFilters.experienceLevel) {
      urlParams.experienceLevel = appliedFilters.experienceLevel[0];
    }
    if (appliedFilters.sortBy) urlParams.sortBy = appliedFilters.sortBy;

    // Handle search parameters - add if search term exists, remove if cleared
    if (hasSearchTerm && searchData.searchField) {
      urlParams.searchTerm = searchData.searchTerm;
      urlParams.searchField = searchData.searchField;
    } else {
      // Explicitly remove search parameters from URL when search is cleared
      currentUrlParams.delete('searchTerm');
      currentUrlParams.delete('searchField');
      // Also remove tab-specific search parameters
      currentUrlParams.delete(`tab${active}_searchTerm`);
      currentUrlParams.delete(`tab${active}_searchField`);
    }

    // If we're clearing search, use the modified URL params, otherwise use updateURL
    if (!hasSearchTerm) {
      // Add other parameters to the cleaned URL
      Object.keys(urlParams).forEach(key => {
        currentUrlParams.set(key, urlParams[key]);
      });

      navigate(`${pathname}?${currentUrlParams.toString()}`, { replace: true });
    } else {
      updateURL(urlParams, active);
    }
  }



  return (
    <>
      <TabNav
        nav={tabs}
        active={active}
        setActive={handleTabClick}
        rightSidebar={
          <div className="flex gap-2 items-center relative">
            <div className="relative">
              <img
                src="/assets/icons/search.svg"
                alt="Search"
                className="w-8 h-8 cursor-pointer"
                onClick={handleSearchClick}
                data-search-icon="true"
              />
              <UniversalSearchDropdown
                isOpen={showSearch}
                onClose={() => setShowSearch(false)}
                onSearch={handleSearch}
                section="candidates"
                activeTab={active}
              />
            </div>
            <EnhancedThreeDot
              dropdownSize="w-56"
              iconSrc="/assets/icons/threedothorizontal.svg"
              buttonDropDown={(onClose) => (
                <div className="min-w-[200px]">
                  <div className="flex items-center justify-between px-4 pt-3 pb-2">
                    <span className="font-medium text-gray-900 text-base">
                      View Option
                    </span>
                    <button
                      className="text-gray-400 hover:text-gray-700 text-lg font-bold focus:outline-none"
                      onClick={(e) => {
                        e.stopPropagation();
                        onClose();
                      }}
                      aria-label="Close"
                      type="button"
                    >
                      ×
                    </button>
                  </div>
                  <hr className="my-1 border-gray-200" />
                  <ul className="py-0" role="none">
                    {active !== 2 && (
                      <li>
                        <div
                          className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                          role="menuitem"
                          onClick={handleFilterClick}
                        >
                          <img src="/assets/icons/filter.svg" alt="Filter" className="w-4 h-4" />
                          Filter
                        </div>
                      </li>
                    )}
                    <li>
                      <div
                        className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                        role="menuitem"
                        onClick={handleSortClick}
                      >
                        <img src="/assets/icons/sorting.svg" alt="Sorting" className="w-4 h-4" />
                        Sorting
                      </div>
                    </li>
                  </ul>
                </div>
              )}
            />

            {active === 3 && (
              <AppButton
                label="+ Add Candidate"
                onClick={() => navigate("/candidate/addcandidate")}
                variant="primary"
              />
            )}
          </div>
        }
      />
      <div className="overflow-x-auto rounded-lg mt-2 border border-gray-200 shadow-sm">
        {showFilter && (
          <FilterBar
            filters={filterOptions[active] || []}
            onClear={() => {}} // Empty function since we'll handle this below
            onApply={() => {}} // Empty function since we'll handle this below
            rightAlignActions={false} // Don't show actions in FilterBar
          />
        )}

        {showSort && (
          <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
            <SubmissionDateSortPill
              selected={sortValue}
              setSelected={handleSortingChange}
              onClose={() => setShowSort(false)}
            />
          </div>
        )}

        {(showFilter || showSort) && (
          <div className="px-4 py-2 bg-white border-b border-gray-200 flex justify-end">
            <div className="flex items-center gap-2 apply-clear-buttons">
              <span className="text-blue-500 cursor-pointer text-xs" onClick={handleClearFilter}>
                Clear all
              </span>
              <button
                className="px-4 py-1 rounded bg-green-500 text-white text-sm font-medium hover:bg-green-600"
                onClick={handleApplyFilter}
              >
                Apply
              </button>
            </div>
          </div>
        )}

        {active === 0 && (
          <CandidateTable
            key={`active-submissions-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={[
              "Name",
              "Job Title",
              "Job Id",
              "Location",
              "Submission Date",
              "Email",
              "Status",
              "",
            ]}
            getData={async (page, limit) => {
              // Combine filter and global search parameters
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

              // Only include search params if search is actually applied
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "activeSubmission",
                page,
                limit,
                combinedParams
              );



              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  location:
                    item?.submission?.job?.location?.country?.split("-")[0],
                  submissionDate: new Date(
                    item?.submission?.submittedAt
                  )?.toLocaleString(),
                  email: item?.personalDetails?.emailAddress,
                  status: item?.submission?.status, // Use submission status, not candidate status
                  submissionId: item?.submission?.submissionId,
                };
              });
              // Debug: Check what we're sending to CandidateTable
              const result = {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };



              return result;
            }}
            dropdownkey="status"
            isDropDownDisable={true}
            dropdownItems={[
              ...JobStatusOptions,
              {
                value: "Talent Pool",
                label: "Talent Pool",
                color: "bg-[#CEFAFE] text-[#00B8DB]",
              },
            ]}
          />
        )}
        {active === 1 && (
          <CandidateTable
            key={`hired-candidates-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={[
              "Name",
              "Job Title",
              "Job Id",
              "Email",
              "Location",
              "Job Type",
              "Experience",
              "",
            ]}
            getData={async (page, limit) => {
              // Combine filter and global search parameters
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

              // Only include search params if search is actually applied
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "hired",
                page,
                limit,
                combinedParams
              );
              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  email: item?.personalDetails?.emailAddress,
                  location: item?.submission?.job?.location?.country
                    ? item?.submission?.job?.location?.country?.split("-")[0]
                    : "-",
                  jobType: item?.submission?.job?.jobType,

                  experience: item?.submission?.job?.experience?.min
                    ? item?.submission?.job?.experience?.min +
                      " - " +
                      item?.submission?.job?.experience?.max +
                      " " +
                      item?.submission?.job?.experience?.unit
                    : "-",
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
          />
        )}
        {active === 2 && (
          <CandidateTable
            key={`rejected-candidates-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={["Name", "Job Title", "Job Id", "Email", "Reason", ""]}
            getData={async (page, limit) => {
              // Combine filter and global search parameters
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

              // Only include search params if search is actually applied
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "rejected",
                page,
                limit,
                combinedParams
              );
              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  email: item?.personalDetails?.emailAddress,
                  reason: item?.submission?.job?.notes
                    ? item?.submission?.job?.notes
                    : "-",
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
          />
        )}
        {active === 3 && (
          <CandidateTable
            key={`all-submissions-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={[
              "Name",
              "Job Id",
              "Job Title",
              "Location",
              "Job Type",
              "Mobile Number",
              "Experience",
              "Email",
              "Status",
              " ",
            ]}
            getData={async (page, limit) => {
              // Combine filter and global search parameters
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

              // Only include search params if search is actually applied
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "any",
                page,
                limit,
                combinedParams
              );
              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,

                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  location: item?.submission?.job?.location?.country
                    ? item?.submission?.job?.location?.country?.split("-")[0]
                    : "-",

                  jobType: item?.submission?.job?.jobType
                    ? item?.submission?.job?.jobType
                    : "-",

                  "Mobile Number": `${
                    item?.personalDetails?.phoneCountryCode
                      ? "+" + item?.personalDetails?.phoneCountryCode
                      : ""
                  } ${
                    item?.personalDetails?.phoneNumber
                      ? item?.personalDetails?.phoneNumber
                      : ""
                  }`,

                  experience: item?.submission?.job?.experience?.min
                    ? item?.submission?.job?.experience?.min +
                      " - " +
                      item?.submission?.job?.experience?.max +
                      " " +
                      item?.submission?.job?.experience?.unit
                    : "-",

                  email: item?.personalDetails?.emailAddress,
                  status: item?.submission?.status, // Use submission status, not candidate status
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
            dropdownkey="status"
            isDropDownDisable={true}
            dropdownItems={[
              ...JobStatusOptions,
              {
                value: "Talent Pool",
                label: "Talent Pool",
                color: "bg-[#CEFAFE] text-[#00B8DB]",
              },
            ]}
          />
        )}
      </div>
    </>
  );
};

export default Candidates;
